# Snake.io - Mobile Responsive Multiplayer Game

A fluid, mobile-responsive Snake.io game built with Node.js, Express, and Socket.io for real-time multiplayer gameplay.

## Features

- **Mobile-First Design**: Optimized for mobile devices with touch controls
- **Smooth Performance**: 60 FPS gameplay with optimized rendering
- **Real-time Multiplayer**: Multiple players can play simultaneously
- **Responsive Controls**: 
  - Touch joystick for mobile devices
  - Keyboard controls (WASD/Arrow keys) for desktop
- **Game Mechanics**:
  - Snake growth by eating food
  - Collision detection with other players
  - Score system and leaderboard
  - Respawn system when colliding

## Installation

1. Make sure you have Node.js installed on your system
2. Install dependencies:
   ```bash
   npm install express socket.io
   ```

## Running the Game

1. Start the server:
   ```bash
   node server.js
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

3. For mobile testing, find your computer's IP address and access:
   ```
   http://YOUR_IP_ADDRESS:3000
   ```

## Controls

### Desktop
- **W/↑**: Move up
- **S/↓**: Move down  
- **A/←**: Move left
- **D/→**: Move right

### Mobile
- Use the virtual joystick in the bottom-right corner
- Drag the joystick knob to change direction

## Game Mechanics

- **Food**: Red circles that increase your score and snake length
- **Growth**: Each food eaten adds to your snake length
- **Speed**: Snake speed increases slightly as you grow
- **Collision**: 
  - Hitting yourself causes respawn with reduced score
  - Smaller snakes die when hitting larger ones
  - Larger snakes gain points from defeating smaller ones

## Performance Optimizations

- **Mobile-specific optimizations**: Reduced render distance and frame limiting
- **Viewport culling**: Only renders objects visible on screen
- **Optimized canvas operations**: Batched drawing for better performance
- **Device pixel ratio support**: Crisp rendering on high-DPI displays

## Technical Details

- **Backend**: Node.js with Express and Socket.io
- **Frontend**: HTML5 Canvas with vanilla JavaScript
- **Real-time Communication**: WebSocket connections via Socket.io
- **Game Loop**: 60 FPS server-side game state updates
- **Responsive Design**: CSS and JavaScript optimizations for all screen sizes

## File Structure

```
├── server.js          # Main server file with game logic
├── package.json       # Project dependencies
├── public/
│   ├── index.html     # Main HTML file
│   └── game.js        # Client-side game logic
└── README.md          # This file
```

## Deployment

For production deployment:

1. Set the PORT environment variable
2. Use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "snake-io"
   ```

## Browser Compatibility

- Chrome/Chromium (recommended for best performance)
- Firefox
- Safari
- Edge
- Mobile browsers (iOS Safari, Chrome Mobile, etc.)

Enjoy playing Snake.io!
