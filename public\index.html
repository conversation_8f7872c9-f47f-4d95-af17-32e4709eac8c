<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>Snake.io - Mobile Responsive</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body {
            background: #1a1a2e;
            font-family: Arial, sans-serif;
            overflow: hidden;
            position: fixed;
            width: 100%;
            height: 100%;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(45deg, #0f3460, #16213e);
        }

        #gameCanvas {
            display: block;
            background: #0f3460;
            touch-action: none;
            width: 100%;
            height: 100%;
        }

        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            z-index: 10;
        }

        #mobileControls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 120px;
            height: 120px;
            z-index: 10;
        }

        .joystick {
            position: relative;
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            touch-action: none;
        }

        .joystick-knob {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.1s ease;
        }

        #leaderboard {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            min-width: 200px;
            z-index: 10;
        }

        .leaderboard-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .leaderboard-entry {
            font-size: 14px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }

        @media (max-width: 768px) {
            #ui {
                font-size: 16px;
                top: 10px;
                left: 10px;
            }
            
            #leaderboard {
                top: 10px;
                right: 10px;
                padding: 10px;
                min-width: 150px;
            }
            
            .leaderboard-title {
                font-size: 14px;
            }
            
            .leaderboard-entry {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <div id="ui">
            <div>Score: <span id="score">0</span></div>
            <div>Length: <span id="length">1</span></div>
        </div>

        <div id="leaderboard">
            <div class="leaderboard-title">Leaderboard</div>
            <div id="leaderboardList"></div>
        </div>

        <div id="mobileControls">
            <div class="joystick" id="joystick">
                <div class="joystick-knob" id="joystickKnob"></div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="game.js"></script>
</body>
</html>
