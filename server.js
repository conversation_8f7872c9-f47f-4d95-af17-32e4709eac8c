const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Game state
const gameState = {
    players: {},
    food: [],
    gameWidth: 2000,
    gameHeight: 2000
};

// Generate random food
function generateFood() {
    return {
        x: Math.random() * (gameState.gameWidth - 20),
        y: Math.random() * (gameState.gameHeight - 20),
        id: Math.random().toString(36).substr(2, 9)
    };
}

// Initialize food
for (let i = 0; i < 50; i++) {
    gameState.food.push(generateFood());
}

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('Player connected:', socket.id);

    // Initialize new player
    gameState.players[socket.id] = {
        id: socket.id,
        x: Math.random() * gameState.gameWidth,
        y: Math.random() * gameState.gameHeight,
        segments: [{
            x: Math.random() * gameState.gameWidth,
            y: Math.random() * gameState.gameHeight
        }],
        direction: { x: 1, y: 0 },
        speed: 3,
        score: 0,
        color: `hsl(${Math.random() * 360}, 70%, 50%)`
    };

    // Send initial game state
    socket.emit('gameState', gameState);

    // Handle player movement
    socket.on('updateDirection', (direction) => {
        if (gameState.players[socket.id]) {
            gameState.players[socket.id].direction = direction;
        }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
        console.log('Player disconnected:', socket.id);
        delete gameState.players[socket.id];
    });
});

// Collision detection functions
function checkSelfCollision(player) {
    const head = player.segments[0];
    for (let i = 4; i < player.segments.length; i++) {
        const segment = player.segments[i];
        const distance = Math.sqrt(
            Math.pow(head.x - segment.x, 2) + Math.pow(head.y - segment.y, 2)
        );
        if (distance < 10) {
            return true;
        }
    }
    return false;
}

function checkPlayerCollision(player1, player2) {
    const head1 = player1.segments[0];
    for (let segment of player2.segments) {
        const distance = Math.sqrt(
            Math.pow(head1.x - segment.x, 2) + Math.pow(head1.y - segment.y, 2)
        );
        if (distance < 12) {
            return true;
        }
    }
    return false;
}

function respawnPlayer(player) {
    player.x = Math.random() * gameState.gameWidth;
    player.y = Math.random() * gameState.gameHeight;
    player.segments = [{
        x: player.x,
        y: player.y
    }];
    player.direction = { x: 1, y: 0 };
    player.score = Math.max(0, Math.floor(player.score * 0.5)); // Lose half score
}

// Game loop
setInterval(() => {
    const playersToRemove = [];

    // Update player positions
    Object.values(gameState.players).forEach(player => {
        const head = player.segments[0];
        const newHead = {
            x: head.x + player.direction.x * player.speed,
            y: head.y + player.direction.y * player.speed
        };

        // Wrap around screen edges
        if (newHead.x < 0) newHead.x = gameState.gameWidth;
        if (newHead.x > gameState.gameWidth) newHead.x = 0;
        if (newHead.y < 0) newHead.y = gameState.gameHeight;
        if (newHead.y > gameState.gameHeight) newHead.y = 0;

        player.segments.unshift(newHead);

        let foodEaten = false;

        // Check food collision
        for (let i = gameState.food.length - 1; i >= 0; i--) {
            const food = gameState.food[i];
            const distance = Math.sqrt(
                Math.pow(newHead.x - food.x, 2) + Math.pow(newHead.y - food.y, 2)
            );
            if (distance < 15) {
                player.score += 10;
                player.speed = Math.min(5, 3 + player.score / 200); // Increase speed slightly
                gameState.food.splice(i, 1);
                gameState.food.push(generateFood());
                foodEaten = true;
                break;
            }
        }

        // Remove tail if no food eaten
        if (!foodEaten) {
            const minLength = 5;
            const maxLength = minLength + Math.floor(player.score / 10);
            if (player.segments.length > maxLength) {
                player.segments.pop();
            }
        }

        // Check self collision
        if (checkSelfCollision(player)) {
            respawnPlayer(player);
        }

        // Check collision with other players
        Object.values(gameState.players).forEach(otherPlayer => {
            if (otherPlayer.id !== player.id) {
                if (checkPlayerCollision(player, otherPlayer)) {
                    // Smaller snake dies
                    if (player.segments.length <= otherPlayer.segments.length) {
                        respawnPlayer(player);
                        otherPlayer.score += Math.floor(player.score * 0.2);
                    }
                }
            }
        });
    });

    // Maintain food count
    while (gameState.food.length < 50) {
        gameState.food.push(generateFood());
    }

    // Broadcast game state to all clients
    io.emit('gameState', gameState);
}, 1000 / 60); // 60 FPS

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Snake.io server running on port ${PORT}`);
});
