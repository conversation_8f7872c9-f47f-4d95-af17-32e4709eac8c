class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.socket = io();

        this.gameState = null;
        this.myPlayerId = null;
        this.camera = { x: 0, y: 0 };
        this.lastTime = 0;
        this.interpolationFactor = 0;

        // Mobile controls
        this.joystick = document.getElementById('joystick');
        this.joystickKnob = document.getElementById('joystickKnob');
        this.isDragging = false;
        this.currentDirection = { x: 1, y: 0 };

        // Performance optimizations
        this.renderDistance = 1000; // Only render objects within this distance
        this.lastRenderTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS;

        // Object pooling for better performance
        this.particlePool = [];
        this.maxParticles = 50;

        // Mobile detection
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        this.setupCanvas();
        this.setupControls();
        this.setupSocketEvents();
        this.startGameLoop();
    }

    setupCanvas() {
        const resizeCanvas = () => {
            // Use device pixel ratio for crisp rendering on high-DPI displays
            const dpr = window.devicePixelRatio || 1;
            const rect = this.canvas.getBoundingClientRect();

            this.canvas.width = rect.width * dpr;
            this.canvas.height = rect.height * dpr;

            this.ctx.scale(dpr, dpr);
            this.canvas.style.width = rect.width + 'px';
            this.canvas.style.height = rect.height + 'px';

            // Optimize canvas for mobile
            if (this.isMobile) {
                this.ctx.imageSmoothingEnabled = false;
                this.renderDistance = 800; // Reduce render distance on mobile
            }
        };

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Prevent scrolling on mobile
        document.addEventListener('touchmove', (e) => e.preventDefault(), { passive: false });

        // Prevent context menu on long press
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    setupControls() {
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            let newDirection = { ...this.currentDirection };
            
            switch(e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    if (this.currentDirection.y === 0) newDirection = { x: 0, y: -1 };
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    if (this.currentDirection.y === 0) newDirection = { x: 0, y: 1 };
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    if (this.currentDirection.x === 0) newDirection = { x: -1, y: 0 };
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    if (this.currentDirection.x === 0) newDirection = { x: 1, y: 0 };
                    break;
            }
            
            if (newDirection.x !== this.currentDirection.x || newDirection.y !== this.currentDirection.y) {
                this.currentDirection = newDirection;
                this.socket.emit('updateDirection', this.currentDirection);
            }
        });

        // Mobile joystick controls
        this.setupJoystick();
    }

    setupJoystick() {
        const joystickCenter = { x: 60, y: 60 };
        const maxDistance = 40;

        const handleStart = (e) => {
            this.isDragging = true;
            e.preventDefault();
        };

        const handleMove = (e) => {
            if (!this.isDragging) return;
            e.preventDefault();

            const rect = this.joystick.getBoundingClientRect();
            const touch = e.touches ? e.touches[0] : e;
            const x = touch.clientX - rect.left - joystickCenter.x;
            const y = touch.clientY - rect.top - joystickCenter.y;

            const distance = Math.sqrt(x * x + y * y);
            const angle = Math.atan2(y, x);

            if (distance > 20) {
                let newDirection = { ...this.currentDirection };
                
                if (Math.abs(x) > Math.abs(y)) {
                    if (x > 0 && this.currentDirection.x === 0) newDirection = { x: 1, y: 0 };
                    else if (x < 0 && this.currentDirection.x === 0) newDirection = { x: -1, y: 0 };
                } else {
                    if (y > 0 && this.currentDirection.y === 0) newDirection = { x: 0, y: 1 };
                    else if (y < 0 && this.currentDirection.y === 0) newDirection = { x: 0, y: -1 };
                }

                if (newDirection.x !== this.currentDirection.x || newDirection.y !== this.currentDirection.y) {
                    this.currentDirection = newDirection;
                    this.socket.emit('updateDirection', this.currentDirection);
                }
            }

            const clampedDistance = Math.min(distance, maxDistance);
            const knobX = Math.cos(angle) * clampedDistance;
            const knobY = Math.sin(angle) * clampedDistance;

            this.joystickKnob.style.transform = `translate(${knobX}px, ${knobY}px)`;
        };

        const handleEnd = (e) => {
            this.isDragging = false;
            this.joystickKnob.style.transform = 'translate(0, 0)';
            e.preventDefault();
        };

        // Touch events
        this.joystick.addEventListener('touchstart', handleStart, { passive: false });
        this.joystick.addEventListener('touchmove', handleMove, { passive: false });
        this.joystick.addEventListener('touchend', handleEnd, { passive: false });

        // Mouse events for desktop testing
        this.joystick.addEventListener('mousedown', handleStart);
        document.addEventListener('mousemove', handleMove);
        document.addEventListener('mouseup', handleEnd);
    }

    setupSocketEvents() {
        this.socket.on('connect', () => {
            this.myPlayerId = this.socket.id;
        });

        this.socket.on('gameState', (gameState) => {
            this.gameState = gameState;
            this.updateUI();
        });
    }

    updateUI() {
        if (!this.gameState || !this.myPlayerId) return;

        const myPlayer = this.gameState.players[this.myPlayerId];
        if (myPlayer) {
            document.getElementById('score').textContent = myPlayer.score;
            document.getElementById('length').textContent = myPlayer.segments.length;
        }

        // Update leaderboard
        const sortedPlayers = Object.values(this.gameState.players)
            .sort((a, b) => b.score - a.score)
            .slice(0, 5);

        const leaderboardList = document.getElementById('leaderboardList');
        leaderboardList.innerHTML = sortedPlayers
            .map((player, index) => 
                `<div class="leaderboard-entry">
                    <span>${index + 1}. ${player.id === this.myPlayerId ? 'You' : 'Player'}</span>
                    <span>${player.score}</span>
                </div>`
            ).join('');
    }

    updateCamera() {
        if (!this.gameState || !this.myPlayerId) return;

        const myPlayer = this.gameState.players[this.myPlayerId];
        if (myPlayer && myPlayer.segments.length > 0) {
            const head = myPlayer.segments[0];
            this.camera.x = head.x - this.canvas.width / 2;
            this.camera.y = head.y - this.canvas.height / 2;
        }
    }

    isInViewport(x, y) {
        const margin = 100; // Extra margin for smooth scrolling
        return (
            x > this.camera.x - margin &&
            x < this.camera.x + this.canvas.width + margin &&
            y > this.camera.y - margin &&
            y < this.camera.y + this.canvas.height + margin
        );
    }

    render() {
        if (!this.gameState) return;

        // Clear canvas with optimized method
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.save();
        this.ctx.translate(-this.camera.x, -this.camera.y);

        // Draw food (only visible ones for performance)
        this.ctx.fillStyle = '#ff6b6b';
        this.gameState.food.forEach(food => {
            if (this.isInViewport(food.x, food.y)) {
                this.ctx.beginPath();
                this.ctx.arc(food.x, food.y, 8, 0, Math.PI * 2);
                this.ctx.fill();
            }
        });

        // Draw players (optimized rendering)
        Object.values(this.gameState.players).forEach(player => {
            // Check if any part of the snake is visible
            const isVisible = player.segments.some(segment =>
                this.isInViewport(segment.x, segment.y)
            );

            if (!isVisible) return;

            this.ctx.fillStyle = player.color;
            this.ctx.strokeStyle = '#ffffff';
            this.ctx.lineWidth = 2;

            // Batch drawing for better performance
            this.ctx.beginPath();
            player.segments.forEach((segment, index) => {
                if (this.isInViewport(segment.x, segment.y)) {
                    const radius = index === 0 ? 12 : Math.max(10 - (index * 0.1), 6);
                    this.ctx.moveTo(segment.x + radius, segment.y);
                    this.ctx.arc(segment.x, segment.y, radius, 0, Math.PI * 2);
                }
            });
            this.ctx.fill();

            // Draw head outline separately for better visibility
            if (player.segments.length > 0) {
                const head = player.segments[0];
                if (this.isInViewport(head.x, head.y)) {
                    this.ctx.beginPath();
                    this.ctx.arc(head.x, head.y, 12, 0, Math.PI * 2);
                    this.ctx.stroke();
                }
            }
        });

        this.ctx.restore();
    }

    startGameLoop() {
        const gameLoop = (currentTime) => {
            // Frame rate limiting for mobile performance
            if (currentTime - this.lastRenderTime < this.frameInterval) {
                requestAnimationFrame(gameLoop);
                return;
            }

            const deltaTime = currentTime - this.lastTime;
            this.lastTime = currentTime;
            this.lastRenderTime = currentTime;

            this.updateCamera();
            this.render();

            requestAnimationFrame(gameLoop);
        };

        requestAnimationFrame(gameLoop);
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new SnakeGame();
});
